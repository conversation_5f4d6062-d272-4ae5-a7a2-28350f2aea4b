# Windows-specific requirements for ERPNext
# Core Frappe Framework dependencies
frappe>=15.0.0

# Database connectivity
mysql-connector-python>=8.0.33
PyMySQL>=1.0.2

# Redis connectivity
redis>=4.5.0
hiredis>=2.2.0

# Windows-specific packages
pywin32>=306
pywin32-ctypes>=0.2.0

# Process management
psutil>=5.9.0

# Web server and networking
gunicorn>=20.1.0
gevent>=22.10.0
eventlet>=0.33.0

# HTTP and requests
requests>=2.28.0
urllib3>=1.26.0

# JSON and data handling
simplejson>=3.18.0
python-dateutil>=2.8.0
pytz>=2022.7

# Cryptography and security
cryptography>=3.4.8
passlib>=1.7.4
bcrypt>=4.0.0

# File handling and compression
Pillow>=9.4.0
zipfile36>=0.1.3

# Email and communication
email-validator>=1.3.0
bleach>=5.0.0

# Development and debugging
ipython>=8.10.0
watchdog>=2.2.0

# Testing (optional)
pytest>=7.2.0
coverage>=7.0.0

# Additional utilities
click>=8.1.0
six>=1.16.0
markupsafe>=2.1.0
jinja2>=3.1.0

# Windows service management
python-windows-service>=1.0.0

# PDF generation
reportlab>=3.6.0
weasyprint>=57.0

# Excel file handling
openpyxl>=3.0.10
xlrd>=2.0.1

# Image processing
wand>=0.6.10

# Barcode generation
python-barcode>=0.14.0
qrcode>=7.3.1

# Payment processing
stripe>=5.0.0
razorpay>=1.3.0

# Social authentication
python-social-auth>=0.3.6

# Caching
python-memcached>=1.59

# Task queue
celery>=5.2.0

# Monitoring
sentry-sdk>=1.14.0

# Configuration management
python-decouple>=3.6

# Logging
structlog>=22.3.0

# API documentation
swagger-ui-bundle>=0.0.9

# Development tools
black>=22.12.0
flake8>=6.0.0
isort>=5.12.0

# Windows-specific file system utilities
pathlib2>=2.3.7
scandir>=1.10.0

# Windows registry access
winreg-unicode>=1.0.0

# Windows event logging
pywin32-eventlog>=1.0.0

# Windows task scheduler
python-crontab>=2.7.0

# Network utilities for Windows
netifaces>=0.11.0
ifaddr>=0.2.0

# Windows clipboard access
pyperclip>=1.8.2

# Windows notification system
plyer>=2.1.0

# Windows-specific HTTP server
waitress>=2.1.0

# Alternative to gunicorn for Windows
cherrypy>=18.8.0

# Windows process utilities
wmi>=1.5.1

# Windows performance counters
pywin32-perfmon>=1.0.0

# Windows COM interface
comtypes>=1.1.14

# Windows shell integration
pywin32-shell>=1.0.0

# Windows service utilities
python-windows-service-manager>=1.0.0

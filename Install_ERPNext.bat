@echo off
REM ERPNext Version 15 Installation Batch File
REM This batch file runs the PowerShell installation script with proper permissions

echo ===============================================
echo    ERPNext Version 15 Installation
echo    Windows 11 with WSL2 Ubuntu 22.04
echo ===============================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges... Good!
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please right-click on this batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo Current directory: %CD%
echo.

REM Check if PowerShell script exists
if not exist "install_erpnext_complete.ps1" (
    echo ERROR: install_erpnext_complete.ps1 not found in current directory!
    echo Please ensure all installation files are in the same folder.
    echo.
    pause
    exit /b 1
)

echo Starting ERPNext installation...
echo This process may take 30-60 minutes depending on your internet connection.
echo.

REM Ask user for confirmation
set /p "confirm=Do you want to proceed with the installation? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Installation cancelled by user.
    pause
    exit /b 0
)

echo.
echo Starting PowerShell installation script...
echo.

REM Run PowerShell script with execution policy bypass
powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_complete.ps1"

REM Check if PowerShell script completed successfully
if %errorLevel% == 0 (
    echo.
    echo ===============================================
    echo    ERPNext Installation Completed Successfully!
    echo ===============================================
    echo.
    echo ERPNext is now installed and ready to use.
    echo.
    echo Access Information:
    echo - URL: http://localhost:8000
    echo - Username: Administrator
    echo - Password: admin123
    echo.
    echo Desktop shortcuts have been created:
    echo - Start_ERPNext.bat
    echo - Stop_ERPNext.bat  
    echo - Open_ERPNext.bat
    echo.
    echo To start ERPNext now, double-click Start_ERPNext.bat on your desktop.
    echo.
) else (
    echo.
    echo ===============================================
    echo    Installation Failed!
    echo ===============================================
    echo.
    echo The installation encountered an error.
    echo Please check the error messages above and try again.
    echo.
    echo For troubleshooting, run: troubleshoot_erpnext.ps1
    echo.
)

echo Press any key to exit...
pause >nul

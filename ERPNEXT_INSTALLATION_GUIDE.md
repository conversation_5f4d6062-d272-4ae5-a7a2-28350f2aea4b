# ERPNext Version 15 Native Windows Installation Guide

This guide provides step-by-step instructions to set up ERPNext version 15 from source on Windows 11 natively without WSL2 or Linux emulation.

## Prerequisites

- Windows 11 with Administrator privileges
- At least 8GB RAM (16GB recommended)
- At least 20GB free disk space
- Stable internet connection

## Installation Overview

The installation process is divided into several phases:

1. **WSL2 Setup**: Install WSL2 with Ubuntu 22.04
2. **Dependencies**: Install Python, Node.js, databases, and other tools
3. **Services**: Configure MariaDB and Redis
4. **Frappe Framework**: Install Frappe Bench CLI
5. **ERPNext**: Install and configure ERPNext v15
6. **Auto-start**: Configure services to start automatically

## Quick Installation

### Step 1: Run the Main Setup Script

1. Open PowerShell as Administrator
2. Navigate to the ERPNext directory:
   ```powershell
   cd "C:\Users\<USER>\Desktop\erpnext-develop"
   ```
3. Run the setup script:
   ```powershell
   .\setup_erpnext_wsl2.ps1
   ```

### Step 2: Complete Ubuntu Setup

When prompted:
1. Create a username and password for Ubuntu
2. Wait for the script to complete the basic setup

### Step 3: Configure Services

Run the services configuration:
```bash
wsl -d Ubuntu-22.04 -- bash -c "cd /mnt/c/Users/<USER>/Desktop/erpnext-develop && chmod +x configure_services.sh && ./configure_services.sh"
```

### Step 4: Install ERPNext

Run the ERPNext installation:
```bash
wsl -d Ubuntu-22.04 -- bash -c "cd /mnt/c/Users/<USER>/Desktop/erpnext-develop && chmod +x install_erpnext.sh && ./install_erpnext.sh"
```

### Step 5: Configure Auto-start

Set up auto-start services:
```bash
wsl -d Ubuntu-22.04 -- bash -c "cd /mnt/c/Users/<USER>/Desktop/erpnext-develop && chmod +x configure_autostart.sh && ./configure_autostart.sh"
```

## Manual Installation Steps

If you prefer to run each step manually:

### 1. Install WSL2 with Ubuntu 22.04

```powershell
# Run as Administrator
wsl --install -d Ubuntu-22.04
```

### 2. Update Ubuntu and Install Dependencies

```bash
# In WSL Ubuntu terminal
sudo apt update && sudo apt upgrade -y
sudo apt install -y git python3 python3-pip python3-dev python3-setuptools python3-venv
sudo apt install -y software-properties-common curl wget build-essential
sudo apt install -y libffi-dev libssl-dev libmysqlclient-dev
sudo apt install -y redis-server supervisor nginx mariadb-server mariadb-client wkhtmltopdf
```

### 3. Create frappeuser

```bash
sudo adduser --disabled-password --gecos "" frappeuser
sudo usermod -aG sudo frappeuser
echo "frappeuser ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/frappeuser
```

### 4. Configure MariaDB

```bash
sudo systemctl start mariadb
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'admin123';"
# Configure UTF-8 support (see configure_services.sh for full config)
```

### 5. Install Node.js via NVM

```bash
# Switch to frappeuser
sudo -u frappeuser bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
npm install -g yarn
```

### 6. Install Frappe Bench

```bash
# As frappeuser
pip3 install frappe-bench
export PATH=$HOME/.local/bin:$PATH
```

### 7. Initialize Frappe Bench

```bash
# As frappeuser
cd /home/<USER>
bench init --frappe-branch version-15 frappe-bench
cd frappe-bench
```

### 8. Create Site and Install ERPNext

```bash
# As frappeuser in frappe-bench directory
bench new-site site1.local --admin-password admin123 --mariadb-root-password admin123
bench get-app erpnext --branch version-15
bench --site site1.local install-app erpnext
bench use site1.local
```

## Starting ERPNext

### Development Server

```bash
# In WSL as frappeuser
cd /home/<USER>/frappe-bench
bench start
```

### Using Windows Batch Files

After running the auto-start configuration, you can use these batch files from your Desktop:

- `start_erpnext.bat` - Start ERPNext development server
- `stop_erpnext.bat` - Stop ERPNext services  
- `check_erpnext_status.bat` - Check service status

## Accessing ERPNext

Once started, ERPNext will be available at:
- **URL**: http://localhost:8000
- **Username**: Administrator
- **Password**: admin123

## Troubleshooting

### Common Issues

1. **Port 8000 already in use**:
   ```bash
   sudo lsof -i :8000
   sudo kill -9 <PID>
   ```

2. **MariaDB connection issues**:
   ```bash
   sudo systemctl restart mariadb
   sudo mysql -u root -padmin123 -e "SHOW DATABASES;"
   ```

3. **Redis connection issues**:
   ```bash
   sudo systemctl restart redis-server
   redis-cli ping
   ```

4. **Permission issues**:
   ```bash
   sudo chown -R frappeuser:frappeuser /home/<USER>/frappe-bench
   ```

### Service Management

```bash
# Check service status
sudo systemctl status mariadb
sudo systemctl status redis-server

# Restart services
sudo systemctl restart mariadb
sudo systemctl restart redis-server

# View logs
sudo journalctl -u mariadb -f
sudo journalctl -u redis-server -f
```

## Production Configuration (Optional)

For production deployment, consider:

1. **Nginx Configuration**: Set up reverse proxy
2. **SSL Certificates**: Use Let's Encrypt
3. **Domain Setup**: Configure proper domain name
4. **Backup Strategy**: Set up automated backups
5. **Security**: Configure firewall and security settings

## Support

For issues and support:
- ERPNext Documentation: https://docs.erpnext.com/
- Frappe Framework: https://frappeframework.com/
- Community Forum: https://discuss.erpnext.com/

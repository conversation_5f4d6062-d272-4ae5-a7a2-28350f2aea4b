{"actions": [], "creation": "2018-11-28 08:55:40.815355", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["payment_document", "payment_entry", "allocated_amount", "clearance_date"], "fields": [{"fieldname": "payment_document", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Document", "options": "DocType", "reqd": 1}, {"fieldname": "payment_entry", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Payment Entry", "options": "payment_document", "reqd": 1}, {"fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Allocated Amount", "reqd": 1}, {"depends_on": "eval:doc.docstatus==1", "fieldname": "clearance_date", "fieldtype": "Date", "label": "Clearance Date", "no_copy": 1, "print_hide": 1, "read_only": 1}], "istable": 1, "links": [], "modified": "2024-03-27 13:06:38.549438", "modified_by": "Administrator", "module": "Accounts", "name": "Bank Transaction Payments", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
{"actions": [], "autoname": "field:bank_name", "creation": "2016-05-04 14:35:00.402544", "doctype": "DocType", "engine": "InnoDB", "field_order": ["settings", "has_print_format", "primary_settings", "bank_name", "cheque_size", "starting_position_from_top_edge", "cheque_width", "cheque_height", "scanned_cheque", "column_break_5", "is_account_payable", "acc_pay_dist_from_top_edge", "acc_pay_dist_from_left_edge", "message_to_show", "date_and_payer_settings", "date_settings", "date_dist_from_top_edge", "date_dist_from_left_edge", "payer_settings", "payer_name_from_top_edge", "payer_name_from_left_edge", "amount_in_words_and_figure_settings", "html_19", "amt_in_words_from_top_edge", "amt_in_words_from_left_edge", "amt_in_word_width", "amt_in_words_line_spacing", "amount_in_figure", "amt_in_figures_from_top_edge", "amt_in_figures_from_left_edge", "account_number_and_signatory_settings", "account_no_settings", "acc_no_dist_from_top_edge", "acc_no_dist_from_left_edge", "signatory_position", "signatory_from_top_edge", "signatory_from_left_edge", "preview", "cheque_print_preview"], "fields": [{"fieldname": "settings", "fieldtype": "HTML", "options": "<div>\n<h3> All dimensions in centimeter only </h3>\n</div>"}, {"default": "0", "fieldname": "has_print_format", "fieldtype": "Check", "hidden": 1, "label": "Has Print Format", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "primary_settings", "fieldtype": "Section Break", "label": "Primary Settings"}, {"fieldname": "bank_name", "fieldtype": "Data", "in_list_view": 1, "label": "Bank Name", "no_copy": 1, "reqd": 1, "unique": 1}, {"default": "Regular", "fieldname": "cheque_size", "fieldtype": "Select", "label": "Cheque Size", "options": "\nRegular\nA4"}, {"depends_on": "eval:doc.cheque_size==\"A4\"", "fieldname": "starting_position_from_top_edge", "fieldtype": "Float", "label": "Starting position from top edge", "precision": "2"}, {"default": "20.00", "fieldname": "cheque_width", "fieldtype": "Float", "label": "Cheque <PERSON>", "precision": "2"}, {"default": "9.00", "fieldname": "cheque_height", "fieldtype": "Float", "label": "Cheque Height", "precision": "2"}, {"fieldname": "scanned_cheque", "fieldtype": "Attach", "label": "Scanned Cheque"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "is_account_payable", "fieldtype": "Check", "label": "Is Account Payable"}, {"default": "1.00", "depends_on": "eval:doc.is_account_payable", "fieldname": "acc_pay_dist_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "9.00", "depends_on": "eval:doc.is_account_payable", "fieldname": "acc_pay_dist_from_left_edge", "fieldtype": "Float", "label": "Distance from left edge", "precision": "2"}, {"default": "Acc. Payee", "depends_on": "eval:doc.is_account_payable", "fieldname": "message_to_show", "fieldtype": "Data", "label": "Message to show"}, {"fieldname": "date_and_payer_settings", "fieldtype": "Section Break"}, {"fieldname": "date_settings", "fieldtype": "HTML", "label": "Date Settings", "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Date Settings</label>"}, {"default": "1.00", "fieldname": "date_dist_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "15.00", "fieldname": "date_dist_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"fieldname": "payer_settings", "fieldtype": "Column Break", "label": "Payer <PERSON>s"}, {"default": "2.00", "fieldname": "payer_name_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "3.00", "fieldname": "payer_name_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"fieldname": "amount_in_words_and_figure_settings", "fieldtype": "Section Break"}, {"fieldname": "html_19", "fieldtype": "HTML", "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Amount In Words</label>"}, {"default": "3.00", "fieldname": "amt_in_words_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "4.00", "fieldname": "amt_in_words_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"default": "15.00", "fieldname": "amt_in_word_width", "fieldtype": "Float", "label": "Width of amount in word", "precision": "2"}, {"default": "0.50", "fieldname": "amt_in_words_line_spacing", "fieldtype": "Float", "label": "Line spacing for amount in words", "precision": "2"}, {"fieldname": "amount_in_figure", "fieldtype": "Column Break", "label": "Amount In Figure"}, {"default": "3.50", "fieldname": "amt_in_figures_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "16.00", "fieldname": "amt_in_figures_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"fieldname": "account_number_and_signatory_settings", "fieldtype": "Section Break"}, {"fieldname": "account_no_settings", "fieldtype": "HTML", "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Account Number Settings</label>"}, {"default": "5.00", "fieldname": "acc_no_dist_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "4.00", "fieldname": "acc_no_dist_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"fieldname": "signatory_position", "fieldtype": "Column Break", "label": "Signatory Position"}, {"default": "6.00", "fieldname": "signatory_from_top_edge", "fieldtype": "Float", "label": "Distance from top edge", "precision": "2"}, {"default": "15.00", "fieldname": "signatory_from_left_edge", "fieldtype": "Float", "label": "Starting location from left edge", "precision": "2"}, {"fieldname": "preview", "fieldtype": "Section Break", "label": "Preview"}, {"fieldname": "cheque_print_preview", "fieldtype": "HTML"}], "links": [], "max_attachments": 1, "modified": "2024-03-27 13:06:44.654989", "modified_by": "Administrator", "module": "Accounts", "name": "Cheque Print Template", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}
{"actions": [], "allow_import": 1, "autoname": "field:coupon_name", "creation": "2018-01-22 14:34:39.701832", "doctype": "DocType", "document_type": "Other", "editable_grid": 1, "engine": "InnoDB", "field_order": ["coupon_name", "coupon_type", "customer", "column_break_4", "coupon_code", "from_external_ecomm_platform", "pricing_rule", "uses", "valid_from", "valid_upto", "maximum_use", "used", "column_break_11", "description", "amended_from"], "fields": [{"description": "e.g. \"Summer Holiday 2019 Offer 20\"", "fieldname": "coupon_name", "fieldtype": "Data", "label": "Coupon Name", "reqd": 1, "unique": 1}, {"fieldname": "coupon_type", "fieldtype": "Select", "in_list_view": 1, "label": "Coupon Type", "options": "Promotional\nGift Card", "reqd": 1}, {"depends_on": "eval: doc.coupon_type == \"Gift Card\"", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"description": "unique e.g. SAVE20  To be used to get discount", "fieldname": "coupon_code", "fieldtype": "Data", "label": "Coupon Code", "no_copy": 1, "set_only_once": 1, "unique": 1}, {"depends_on": "eval: !doc.from_external_ecomm_platform", "fieldname": "pricing_rule", "fieldtype": "Link", "label": "Pricing Rule", "mandatory_depends_on": "eval: !doc.from_external_ecomm_platform", "options": "Pricing Rule"}, {"fieldname": "uses", "fieldtype": "Section Break", "label": "Validity and Usage"}, {"fieldname": "valid_from", "fieldtype": "Date", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"fieldname": "valid_upto", "fieldtype": "Date", "label": "Valid Up To"}, {"depends_on": "eval: doc.coupon_type == \"Promotional\"", "fieldname": "maximum_use", "fieldtype": "Int", "label": "Maximum Use"}, {"default": "0", "fieldname": "used", "fieldtype": "Int", "label": "Used", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Coupon Description"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Coupon Code", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "from_external_ecomm_platform", "fieldtype": "Check", "label": "From External Ecomm Platform"}], "links": [], "modified": "2024-11-19 16:35:11.836441", "modified_by": "Administrator", "module": "Accounts", "name": "Coupon Code", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "coupon_name", "track_changes": 1}
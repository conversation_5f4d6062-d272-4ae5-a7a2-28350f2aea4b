{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2017-05-29 21:35:13.136357", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["account_name", "account", "bank", "account_type", "account_subtype", "column_break_7", "disabled", "is_default", "is_company_account", "company", "section_break_11", "party_type", "column_break_14", "party", "account_details_section", "iban", "column_break_12", "branch_code", "bank_account_no", "address_and_contact", "address_html", "column_break_13", "contact_html", "integration_details_section", "integration_id", "last_integration_date", "column_break_27", "mask"], "fields": [{"fieldname": "account_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Account Name", "reqd": 1}, {"depends_on": "is_company_account", "fieldname": "account", "fieldtype": "Link", "in_list_view": 1, "label": "Company Account", "options": "Account"}, {"fieldname": "bank", "fieldtype": "Link", "label": "Bank", "options": "Bank", "reqd": 1}, {"fieldname": "account_type", "fieldtype": "Link", "label": "Account Type", "options": "Bank Account Type"}, {"fieldname": "account_subtype", "fieldtype": "Link", "label": "Account Subtype", "options": "Bank Account Subtype"}, {"fieldname": "column_break_7", "fieldtype": "Column Break", "search_index": 1}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "label": "<PERSON> Default Account"}, {"default": "0", "description": "Setting the account as a Company Account is necessary for Bank Reconciliation", "fieldname": "is_company_account", "fieldtype": "Check", "label": "Is Company Account"}, {"depends_on": "is_company_account", "fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company"}, {"depends_on": "eval:!doc.is_company_account", "fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Party Details"}, {"fieldname": "party_type", "fieldtype": "Link", "label": "Party Type", "options": "DocType"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "party", "fieldtype": "Dynamic Link", "label": "Party", "options": "party_type"}, {"fieldname": "account_details_section", "fieldtype": "Section Break", "label": "Account Details"}, {"fieldname": "iban", "fieldtype": "Data", "in_list_view": 1, "label": "IBAN", "length": 30}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "bank_account_no", "fieldtype": "Data", "in_list_view": 1, "label": "Bank Account No", "length": 30}, {"fieldname": "address_and_contact", "fieldtype": "Section Break", "label": "Address and Contact", "options": "fa fa-map-marker"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML"}, {"fieldname": "integration_details_section", "fieldtype": "Section Break", "label": "Integration Details"}, {"fieldname": "integration_id", "fieldtype": "Data", "hidden": 1, "label": "Integration ID", "no_copy": 1, "read_only": 1, "unique": 1}, {"description": "Change this date manually to setup the next synchronization start date", "fieldname": "last_integration_date", "fieldtype": "Date", "label": "Last Integration Date"}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "mask", "fieldtype": "Data", "label": "Mask", "read_only": 1}, {"fieldname": "branch_code", "fieldtype": "Data", "in_global_search": 1, "label": "Branch Code"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}], "links": [{"group": "Transactions", "link_doctype": "Payment Request", "link_fieldname": "bank_account"}, {"group": "Transactions", "link_doctype": "Payment Order", "link_fieldname": "bank_account"}, {"group": "Transactions", "link_doctype": "Bank Guarantee", "link_fieldname": "bank_account"}, {"group": "Transactions", "link_doctype": "Bank Transaction", "link_fieldname": "bank_account"}, {"group": "Accounting", "link_doctype": "Payment Entry", "link_fieldname": "bank_account"}, {"group": "Accounting", "link_doctype": "Journal Entry", "link_fieldname": "bank_account"}, {"group": "Party", "link_doctype": "Customer", "link_fieldname": "default_bank_account"}, {"group": "Party", "link_doctype": "Supplier", "link_fieldname": "default_bank_account"}], "modified": "2024-10-30 09:41:14.113414", "modified_by": "Administrator", "module": "Accounts", "name": "Bank Account", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "search_fields": "bank,account", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
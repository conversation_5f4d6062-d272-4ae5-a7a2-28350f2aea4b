#!/usr/bin/env python3
"""
Windows Service Manager for ERPNext
Manages MariaDB, Redis, and ERPNext services on Windows
"""

import os
import sys
import subprocess
import time
import json
import psutil
import win32service
import win32serviceutil
import win32api
import win32con
import win32event
import win32evtlogutil
import servicemanager
import socket
from pathlib import Path


class ERPNextWindowsService(win32serviceutil.ServiceFramework):
    """Windows service for ERPNext"""
    
    _svc_name_ = "ERPNextService"
    _svc_display_name_ = "ERPNext Development Server"
    _svc_description_ = "ERPNext ERP System Development Server"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        
        # Get bench path from registry or use default
        self.bench_path = self._get_bench_path()
        self.processes = []
    
    def _get_bench_path(self):
        """Get bench path from configuration"""
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SOFTWARE\ERPNext\Config")
            bench_path, _ = winreg.QueryValueEx(key, "BenchPath")
            winreg.CloseKey(key)
            return Path(bench_path)
        except:
            return Path.cwd()
    
    def SvcStop(self):
        """Stop the service"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        
        # Stop all processes
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=10)
            except:
                try:
                    process.kill()
                except:
                    pass
    
    def SvcDoRun(self):
        """Run the service"""
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                            servicemanager.PYS_SERVICE_STARTED,
                            (self._svc_name_, ''))
        
        self.main()
    
    def main(self):
        """Main service loop"""
        try:
            # Start ERPNext server
            self._start_erpnext_server()
            
            # Wait for stop signal
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            
        except Exception as e:
            servicemanager.LogErrorMsg(f"ERPNext Service error: {str(e)}")
    
    def _start_erpnext_server(self):
        """Start the ERPNext development server"""
        venv_python = self.bench_path / "env" / "Scripts" / "python.exe"
        
        if not venv_python.exists():
            raise Exception(f"Python virtual environment not found at {venv_python}")
        
        # Set environment
        env = os.environ.copy()
        env["PYTHONPATH"] = str(self.bench_path)
        
        # Start the server process
        cmd = [str(venv_python), "-m", "frappe", "serve", "--port", "8000"]
        
        process = subprocess.Popen(
            cmd,
            cwd=str(self.bench_path),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        self.processes.append(process)
        servicemanager.LogInfoMsg(f"ERPNext server started with PID {process.pid}")


class WindowsServiceManager:
    """Manager for Windows services related to ERPNext"""
    
    def __init__(self):
        self.services = {
            "mariadb": "MySQL",  # Default MariaDB service name
            "redis": "Redis",    # Redis service name
            "erpnext": "ERPNextService"
        }
    
    def install_erpnext_service(self, bench_path):
        """Install ERPNext as a Windows service"""
        try:
            # Store bench path in registry
            self._store_bench_path(bench_path)
            
            # Install the service
            win32serviceutil.InstallService(
                ERPNextWindowsService._svc_reg_class_,
                ERPNextWindowsService._svc_name_,
                ERPNextWindowsService._svc_display_name_,
                description=ERPNextWindowsService._svc_description_
            )
            print("ERPNext service installed successfully")
            
        except Exception as e:
            print(f"Error installing ERPNext service: {e}")
    
    def uninstall_erpnext_service(self):
        """Uninstall ERPNext Windows service"""
        try:
            win32serviceutil.RemoveService(ERPNextWindowsService._svc_name_)
            print("ERPNext service uninstalled successfully")
        except Exception as e:
            print(f"Error uninstalling ERPNext service: {e}")
    
    def start_service(self, service_name):
        """Start a Windows service"""
        try:
            if service_name in self.services:
                actual_service = self.services[service_name]
            else:
                actual_service = service_name
            
            win32serviceutil.StartService(actual_service)
            print(f"Service {actual_service} started successfully")
            return True
            
        except Exception as e:
            print(f"Error starting service {service_name}: {e}")
            return False
    
    def stop_service(self, service_name):
        """Stop a Windows service"""
        try:
            if service_name in self.services:
                actual_service = self.services[service_name]
            else:
                actual_service = service_name
            
            win32serviceutil.StopService(actual_service)
            print(f"Service {actual_service} stopped successfully")
            return True
            
        except Exception as e:
            print(f"Error stopping service {service_name}: {e}")
            return False
    
    def get_service_status(self, service_name):
        """Get status of a Windows service"""
        try:
            if service_name in self.services:
                actual_service = self.services[service_name]
            else:
                actual_service = service_name
            
            status = win32serviceutil.QueryServiceStatus(actual_service)
            state = status[1]
            
            states = {
                win32service.SERVICE_STOPPED: "Stopped",
                win32service.SERVICE_START_PENDING: "Starting",
                win32service.SERVICE_STOP_PENDING: "Stopping",
                win32service.SERVICE_RUNNING: "Running",
                win32service.SERVICE_CONTINUE_PENDING: "Continuing",
                win32service.SERVICE_PAUSE_PENDING: "Pausing",
                win32service.SERVICE_PAUSED: "Paused"
            }
            
            return states.get(state, "Unknown")
            
        except Exception as e:
            return f"Error: {e}"
    
    def start_all_services(self):
        """Start all ERPNext-related services"""
        print("Starting all ERPNext services...")
        
        # Start MariaDB
        if self.start_service("mariadb"):
            time.sleep(2)  # Wait for MariaDB to fully start
        
        # Start Redis
        if self.start_service("redis"):
            time.sleep(1)  # Wait for Redis to start
        
        # Start ERPNext
        self.start_service("erpnext")
        
        print("All services startup completed")
    
    def stop_all_services(self):
        """Stop all ERPNext-related services"""
        print("Stopping all ERPNext services...")
        
        # Stop in reverse order
        self.stop_service("erpnext")
        self.stop_service("redis")
        self.stop_service("mariadb")
        
        print("All services stopped")
    
    def status_all_services(self):
        """Get status of all ERPNext-related services"""
        print("ERPNext Services Status:")
        print("-" * 40)
        
        for service_name, actual_service in self.services.items():
            status = self.get_service_status(service_name)
            print(f"{service_name.ljust(15)}: {status}")
    
    def _store_bench_path(self, bench_path):
        """Store bench path in Windows registry"""
        try:
            import winreg
            key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, 
                                 r"SOFTWARE\ERPNext\Config")
            winreg.SetValueEx(key, "BenchPath", 0, winreg.REG_SZ, str(bench_path))
            winreg.CloseKey(key)
        except Exception as e:
            print(f"Warning: Could not store bench path in registry: {e}")
    
    def check_dependencies(self):
        """Check if required services are installed"""
        print("Checking ERPNext dependencies...")
        
        dependencies = {
            "MariaDB": ["MySQL", "MariaDB"],
            "Redis": ["Redis"],
        }
        
        for dep_name, service_names in dependencies.items():
            found = False
            for service_name in service_names:
                try:
                    win32serviceutil.QueryServiceStatus(service_name)
                    print(f"✓ {dep_name} service found: {service_name}")
                    found = True
                    break
                except:
                    continue
            
            if not found:
                print(f"✗ {dep_name} service not found")
        
        # Check if ports are available
        self._check_ports()
    
    def _check_ports(self):
        """Check if required ports are available"""
        ports = {
            3306: "MariaDB",
            6379: "Redis", 
            8000: "ERPNext Web Server"
        }
        
        print("\nChecking port availability...")
        for port, service in ports.items():
            if self._is_port_in_use(port):
                print(f"✗ Port {port} ({service}) is in use")
            else:
                print(f"✓ Port {port} ({service}) is available")
    
    def _is_port_in_use(self, port):
        """Check if a port is in use"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0


def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ERPNext Windows Service Manager")
    parser.add_argument("action", choices=[
        "install", "uninstall", "start", "stop", "restart", 
        "status", "check", "start-all", "stop-all"
    ], help="Action to perform")
    parser.add_argument("--service", help="Specific service name")
    parser.add_argument("--bench-path", help="Path to bench directory")
    
    args = parser.parse_args()
    
    manager = WindowsServiceManager()
    
    if args.action == "install":
        if not args.bench_path:
            args.bench_path = input("Enter bench path: ")
        manager.install_erpnext_service(args.bench_path)
    
    elif args.action == "uninstall":
        manager.uninstall_erpnext_service()
    
    elif args.action == "start":
        if args.service:
            manager.start_service(args.service)
        else:
            manager.start_all_services()
    
    elif args.action == "stop":
        if args.service:
            manager.stop_service(args.service)
        else:
            manager.stop_all_services()
    
    elif args.action == "restart":
        if args.service:
            manager.stop_service(args.service)
            time.sleep(2)
            manager.start_service(args.service)
        else:
            manager.stop_all_services()
            time.sleep(3)
            manager.start_all_services()
    
    elif args.action == "status":
        manager.status_all_services()
    
    elif args.action == "check":
        manager.check_dependencies()
    
    elif args.action == "start-all":
        manager.start_all_services()
    
    elif args.action == "stop-all":
        manager.stop_all_services()


if __name__ == "__main__":
    if len(sys.argv) == 1:
        main()
    else:
        # Handle service installation/removal
        win32serviceutil.HandleCommandLine(ERPNextWindowsService)

@echo off
REM ERPNext Version 15 Advanced Installation Batch File
REM This batch file provides options for step-by-step installation

echo ===============================================
echo    ERPNext Version 15 Advanced Installation
echo    Windows 11 with WSL2 Ubuntu 22.04
echo ===============================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges... Good!
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please right-click on this batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:MENU
echo ===============================================
echo    Installation Options
echo ===============================================
echo.
echo 1. Complete Installation (Recommended)
echo 2. Install WSL2 Only
echo 3. Install Dependencies Only (Skip WSL2)
echo 4. Install ERPNext Only (Skip WSL2 and Dependencies)
echo 5. Troubleshoot Existing Installation
echo 6. View Installation Guide
echo 7. Exit
echo.
set /p "choice=Please select an option (1-7): "

if "%choice%"=="1" goto COMPLETE
if "%choice%"=="2" goto WSL_ONLY
if "%choice%"=="3" goto DEPS_ONLY
if "%choice%"=="4" goto ERPNEXT_ONLY
if "%choice%"=="5" goto TROUBLESHOOT
if "%choice%"=="6" goto VIEW_GUIDE
if "%choice%"=="7" goto EXIT
echo Invalid choice. Please try again.
echo.
goto MENU

:COMPLETE
echo.
echo Starting complete ERPNext installation...
powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_complete.ps1"
goto COMPLETION

:WSL_ONLY
echo.
echo Installing WSL2 with Ubuntu 22.04 only...
powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_complete.ps1" -SkipDependencies -SkipERPNext
echo.
echo WSL2 installation completed. Please restart your computer if prompted.
echo After restart, run this script again and choose option 3.
goto COMPLETION

:DEPS_ONLY
echo.
echo Installing dependencies only (skipping WSL2 installation)...
powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_complete.ps1" -SkipWSLInstall -SkipERPNext
echo.
echo Dependencies installation completed.
echo Run this script again and choose option 4 to install ERPNext.
goto COMPLETION

:ERPNEXT_ONLY
echo.
echo Installing ERPNext only (assuming WSL2 and dependencies are ready)...
powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_complete.ps1" -SkipWSLInstall -SkipDependencies
goto COMPLETION

:TROUBLESHOOT
echo.
echo Running troubleshooting tool...
if exist "troubleshoot_erpnext.ps1" (
    powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -All
) else (
    echo Troubleshooting script not found!
)
echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:VIEW_GUIDE
echo.
echo Opening installation guide...
if exist "ERPNEXT_INSTALLATION_GUIDE.md" (
    start notepad "ERPNEXT_INSTALLATION_GUIDE.md"
) else (
    echo Installation guide not found!
)
echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:COMPLETION
if %errorLevel% == 0 (
    echo.
    echo ===============================================
    echo    Installation Step Completed Successfully!
    echo ===============================================
    echo.
) else (
    echo.
    echo ===============================================
    echo    Installation Step Failed!
    echo ===============================================
    echo.
    echo Please check the error messages above.
)

echo.
set /p "continue=Do you want to return to the menu? (Y/N): "
if /i "%continue%"=="Y" goto MENU

:EXIT
echo.
echo Thank you for using ERPNext installer!
echo.
echo If ERPNext is fully installed, you can:
echo - Start ERPNext: Double-click Start_ERPNext.bat on desktop
echo - Access ERPNext: http://localhost:8000
echo - Username: Administrator, Password: admin123
echo.
echo Press any key to exit...
pause >nul

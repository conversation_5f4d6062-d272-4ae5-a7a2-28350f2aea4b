#!/usr/bin/env python3
"""
Windows Process Manager for ERPNext
Replaces Procfile functionality for Windows
"""

import os
import sys
import subprocess
import threading
import time
import signal
import json
import psutil
from pathlib import Path
import configparser
import logging
from datetime import datetime


class WindowsProcessManager:
    """Manages ERPNext processes on Windows"""
    
    def __init__(self, bench_path=None):
        self.bench_path = Path(bench_path) if bench_path else Path.cwd()
        self.processes = {}
        self.running = False
        self.logs_path = self.bench_path / "logs"
        self.logs_path.mkdir(exist_ok=True)
        
        # Setup logging
        self.setup_logging()
        
        # Process definitions (equivalent to Procfile)
        self.process_definitions = {
            "web": {
                "command": [sys.executable, "-m", "frappe", "serve", "--port", "8000"],
                "env": {"PYTHONPATH": str(self.bench_path)},
                "cwd": str(self.bench_path),
                "restart": True,
                "restart_delay": 5
            },
            "worker": {
                "command": [sys.executable, "-m", "frappe", "worker"],
                "env": {"PYTHONPATH": str(self.bench_path)},
                "cwd": str(self.bench_path),
                "restart": True,
                "restart_delay": 10
            },
            "schedule": {
                "command": [sys.executable, "-m", "frappe", "schedule"],
                "env": {"PYTHONPATH": str(self.bench_path)},
                "cwd": str(self.bench_path),
                "restart": True,
                "restart_delay": 15
            },
            "socketio": {
                "command": [sys.executable, "-m", "frappe", "socketio"],
                "env": {"PYTHONPATH": str(self.bench_path)},
                "cwd": str(self.bench_path),
                "restart": True,
                "restart_delay": 5
            }
        }
    
    def setup_logging(self):
        """Setup logging for process manager"""
        log_file = self.logs_path / "process_manager.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("ProcessManager")
    
    def start_all(self, processes=None):
        """Start all or specified processes"""
        if processes is None:
            processes = list(self.process_definitions.keys())
        
        self.running = True
        self.logger.info("Starting ERPNext processes...")
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start each process
        for process_name in processes:
            if process_name in self.process_definitions:
                self.start_process(process_name)
            else:
                self.logger.warning(f"Unknown process: {process_name}")
        
        # Monitor processes
        self._monitor_processes()
    
    def start_process(self, process_name):
        """Start a specific process"""
        if process_name in self.processes:
            self.logger.warning(f"Process {process_name} is already running")
            return
        
        definition = self.process_definitions.get(process_name)
        if not definition:
            self.logger.error(f"No definition found for process: {process_name}")
            return
        
        self.logger.info(f"Starting process: {process_name}")
        
        # Setup environment
        env = os.environ.copy()
        env.update(definition.get("env", {}))
        
        # Setup log files
        stdout_log = self.logs_path / f"{process_name}.log"
        stderr_log = self.logs_path / f"{process_name}_error.log"
        
        try:
            # Start the process
            process = subprocess.Popen(
                definition["command"],
                cwd=definition.get("cwd", str(self.bench_path)),
                env=env,
                stdout=open(stdout_log, "a"),
                stderr=open(stderr_log, "a"),
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            self.processes[process_name] = {
                "process": process,
                "definition": definition,
                "start_time": datetime.now(),
                "restart_count": 0,
                "stdout_log": stdout_log,
                "stderr_log": stderr_log
            }
            
            self.logger.info(f"Process {process_name} started with PID {process.pid}")
            
        except Exception as e:
            self.logger.error(f"Failed to start process {process_name}: {e}")
    
    def stop_process(self, process_name):
        """Stop a specific process"""
        if process_name not in self.processes:
            self.logger.warning(f"Process {process_name} is not running")
            return
        
        process_info = self.processes[process_name]
        process = process_info["process"]
        
        self.logger.info(f"Stopping process: {process_name}")
        
        try:
            # Try graceful shutdown first
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # Force kill if graceful shutdown fails
                self.logger.warning(f"Force killing process {process_name}")
                process.kill()
                process.wait()
            
            # Close log files
            if process.stdout:
                process.stdout.close()
            if process.stderr:
                process.stderr.close()
            
            del self.processes[process_name]
            self.logger.info(f"Process {process_name} stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping process {process_name}: {e}")
    
    def stop_all(self):
        """Stop all processes"""
        self.running = False
        self.logger.info("Stopping all processes...")
        
        for process_name in list(self.processes.keys()):
            self.stop_process(process_name)
        
        self.logger.info("All processes stopped")
    
    def restart_process(self, process_name):
        """Restart a specific process"""
        self.logger.info(f"Restarting process: {process_name}")
        self.stop_process(process_name)
        time.sleep(2)  # Brief delay before restart
        self.start_process(process_name)
    
    def get_status(self):
        """Get status of all processes"""
        status = {}
        for process_name, process_info in self.processes.items():
            process = process_info["process"]
            try:
                # Check if process is still running
                if process.poll() is None:
                    status[process_name] = {
                        "status": "running",
                        "pid": process.pid,
                        "start_time": process_info["start_time"].isoformat(),
                        "restart_count": process_info["restart_count"]
                    }
                else:
                    status[process_name] = {
                        "status": "stopped",
                        "exit_code": process.returncode,
                        "restart_count": process_info["restart_count"]
                    }
            except Exception as e:
                status[process_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return status
    
    def _monitor_processes(self):
        """Monitor processes and restart if needed"""
        while self.running:
            try:
                for process_name in list(self.processes.keys()):
                    process_info = self.processes[process_name]
                    process = process_info["process"]
                    definition = process_info["definition"]
                    
                    # Check if process has died
                    if process.poll() is not None:
                        exit_code = process.returncode
                        self.logger.warning(f"Process {process_name} died with exit code {exit_code}")
                        
                        # Remove from processes list
                        del self.processes[process_name]
                        
                        # Restart if configured to do so
                        if definition.get("restart", False) and self.running:
                            restart_delay = definition.get("restart_delay", 5)
                            self.logger.info(f"Restarting {process_name} in {restart_delay} seconds...")
                            
                            time.sleep(restart_delay)
                            
                            if self.running:  # Check if we're still supposed to be running
                                process_info["restart_count"] += 1
                                self.start_process(process_name)
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in process monitor: {e}")
                time.sleep(5)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop_all()
        sys.exit(0)
    
    def tail_logs(self, process_name=None, lines=50):
        """Show recent log entries"""
        if process_name:
            if process_name in self.processes:
                log_file = self.processes[process_name]["stdout_log"]
                self._tail_file(log_file, lines)
            else:
                print(f"Process {process_name} not found")
        else:
            # Show logs for all processes
            for name in self.processes:
                print(f"\n=== {name} ===")
                log_file = self.processes[name]["stdout_log"]
                self._tail_file(log_file, lines)
    
    def _tail_file(self, file_path, lines):
        """Show last N lines of a file"""
        try:
            with open(file_path, 'r') as f:
                file_lines = f.readlines()
                for line in file_lines[-lines:]:
                    print(line.rstrip())
        except FileNotFoundError:
            print(f"Log file not found: {file_path}")
        except Exception as e:
            print(f"Error reading log file: {e}")


def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ERPNext Windows Process Manager")
    parser.add_argument("action", choices=[
        "start", "stop", "restart", "status", "logs"
    ], help="Action to perform")
    parser.add_argument("--process", help="Specific process name")
    parser.add_argument("--bench-path", help="Path to bench directory")
    parser.add_argument("--lines", type=int, default=50, help="Number of log lines to show")
    
    args = parser.parse_args()
    
    manager = WindowsProcessManager(args.bench_path)
    
    if args.action == "start":
        if args.process:
            manager.start_process(args.process)
            # Keep running to monitor the process
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_process(args.process)
        else:
            manager.start_all()
    
    elif args.action == "stop":
        if args.process:
            manager.stop_process(args.process)
        else:
            manager.stop_all()
    
    elif args.action == "restart":
        if args.process:
            manager.restart_process(args.process)
        else:
            manager.stop_all()
            time.sleep(2)
            manager.start_all()
    
    elif args.action == "status":
        status = manager.get_status()
        print("\nERPNext Process Status:")
        print("-" * 50)
        for process_name, info in status.items():
            print(f"{process_name.ljust(15)}: {info['status']}")
            if info['status'] == 'running':
                print(f"{''.ljust(15)}  PID: {info['pid']}")
                print(f"{''.ljust(15)}  Started: {info['start_time']}")
                print(f"{''.ljust(15)}  Restarts: {info['restart_count']}")
    
    elif args.action == "logs":
        manager.tail_logs(args.process, args.lines)


if __name__ == "__main__":
    main()

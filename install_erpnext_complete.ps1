# Complete ERPNext Version 15 Installation Script for Windows 11 with WSL2
# This script automates the entire installation process

param(
    [switch]$SkipWSLInstall,
    [switch]$SkipDependencies,
    [switch]$SkipERPNext,
    [string]$MariaDBPassword = "admin123",
    [string]$AdminPassword = "admin123"
)

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

$ErrorActionPreference = "Stop"

Write-Host "=== ERPNext Version 15 Complete Installation ===" -ForegroundColor Green
Write-Host "This script will install ERPNext v15 with all dependencies on WSL2 Ubuntu 22.04" -ForegroundColor Yellow
Write-Host ""

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run PowerShell as Administrator." -ForegroundColor Red
    exit 1
}

# Function to check WSL status
function Test-WSLInstalled {
    try {
        $wslList = wsl --list --quiet 2>$null
        return $wslList -contains "Ubuntu-22.04"
    } catch {
        return $false
    }
}

# Function to wait for WSL to be ready
function Wait-WSLReady {
    Write-Host "Waiting for WSL to be ready..." -ForegroundColor Yellow
    do {
        Start-Sleep -Seconds 5
        try {
            $result = wsl -d Ubuntu-22.04 -- echo "ready" 2>$null
            if ($result -eq "ready") {
                return $true
            }
        } catch {
            # Continue waiting
        }
    } while ($true)
}

# Phase 1: Install WSL2 if not skipped
if (-not $SkipWSLInstall) {
    Write-Host "`n=== Phase 1: Installing WSL2 with Ubuntu 22.04 ===" -ForegroundColor Cyan
    
    if (Test-WSLInstalled) {
        Write-Host "Ubuntu 22.04 is already installed in WSL2" -ForegroundColor Green
    } else {
        Write-Host "Installing WSL2 with Ubuntu 22.04..." -ForegroundColor Yellow
        try {
            wsl --install -d Ubuntu-22.04
            Write-Host "WSL2 installation completed. Please restart your computer if prompted." -ForegroundColor Green
            Write-Host "After restart, run this script again with -SkipWSLInstall parameter." -ForegroundColor Yellow
            Read-Host "Press Enter to continue or Ctrl+C to exit and restart"
        } catch {
            Write-Host "Error installing WSL2: $_" -ForegroundColor Red
            exit 1
        }
    }
    
    # Wait for WSL to be ready
    Wait-WSLReady
}

# Phase 2: Install dependencies if not skipped
if (-not $SkipDependencies) {
    Write-Host "`n=== Phase 2: Installing Dependencies ===" -ForegroundColor Cyan
    
    try {
        Write-Host "Installing system dependencies..." -ForegroundColor Yellow
        wsl -d Ubuntu-22.04 -- bash -c @"
set -e
sudo apt update && sudo apt upgrade -y
sudo apt install -y git python3 python3-pip python3-dev python3-setuptools python3-venv
sudo apt install -y software-properties-common curl wget build-essential
sudo apt install -y libffi-dev libssl-dev libmysqlclient-dev
sudo apt install -y redis-server supervisor nginx mariadb-server mariadb-client wkhtmltopdf

# Create frappeuser
if ! id 'frappeuser' &>/dev/null; then
    sudo adduser --disabled-password --gecos '' frappeuser
    sudo usermod -aG sudo frappeuser
    echo 'frappeuser ALL=(ALL) NOPASSWD:ALL' | sudo tee /etc/sudoers.d/frappeuser
fi

echo 'Dependencies installation completed'
"@
        
        Write-Host "Configuring services..." -ForegroundColor Yellow
        wsl -d Ubuntu-22.04 -- bash -c @"
set -e

# Configure MariaDB
sudo systemctl start mariadb
sudo systemctl enable mariadb
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MariaDBPassword';"
sudo mysql -u root -p$MariaDBPassword -e "DELETE FROM mysql.user WHERE User='';"
sudo mysql -u root -p$MariaDBPassword -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
sudo mysql -u root -p$MariaDBPassword -e "DROP DATABASE IF EXISTS test;"
sudo mysql -u root -p$MariaDBPassword -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
sudo mysql -u root -p$MariaDBPassword -e "FLUSH PRIVILEGES;"

# Configure MariaDB for UTF-8
sudo tee /etc/mysql/mariadb.conf.d/50-server.cnf > /dev/null <<EOF
[server]
user = mysql
pid-file = /run/mysqld/mysqld.pid
socket = /run/mysqld/mysqld.sock
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql
bind-address = 127.0.0.1
query_cache_size = 16M
log_error = /var/log/mysql/error.log

[mysqld]
innodb-file-format=barracuda
innodb-file-per-table=1
innodb-large-prefix=1
character-set-client-handshake = FALSE
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

[mysql]
default-character-set = utf8mb4
EOF

sudo systemctl restart mariadb

# Configure Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
sudo sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
sudo sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
sudo systemctl restart redis-server

echo 'Services configuration completed'
"@
        
        Write-Host "Installing Node.js and Yarn..." -ForegroundColor Yellow
        wsl -d Ubuntu-22.04 -- sudo -u frappeuser bash -c @"
set -e
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
export NVM_DIR='\$HOME/.nvm'
[ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh'
nvm install 18
nvm use 18
nvm alias default 18
npm install -g yarn
echo 'Node.js and Yarn installation completed'
"@
        
        Write-Host "Dependencies installation completed!" -ForegroundColor Green
        
    } catch {
        Write-Host "Error installing dependencies: $_" -ForegroundColor Red
        exit 1
    }
}

# Phase 3: Install ERPNext if not skipped
if (-not $SkipERPNext) {
    Write-Host "`n=== Phase 3: Installing ERPNext ===" -ForegroundColor Cyan
    
    try {
        Write-Host "Installing Frappe Bench and ERPNext..." -ForegroundColor Yellow
        wsl -d Ubuntu-22.04 -- sudo -u frappeuser bash -c @"
set -e
export NVM_DIR='\$HOME/.nvm'
[ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh'
nvm use 18

# Install Frappe Bench
pip3 install frappe-bench
echo 'export PATH=\$HOME/.local/bin:\$PATH' >> ~/.bashrc
export PATH=\$HOME/.local/bin:\$PATH

# Initialize bench
cd /home/<USER>
bench init --frappe-branch version-15 frappe-bench
cd frappe-bench

# Create site
bench new-site site1.local --admin-password $AdminPassword --mariadb-root-password $MariaDBPassword

# Get and install ERPNext
bench get-app erpnext --branch version-15
bench --site site1.local install-app erpnext

# Configure site
bench --site site1.local set-config developer_mode 1
bench --site site1.local clear-cache
bench use site1.local

echo 'ERPNext installation completed'
"@
        
        Write-Host "ERPNext installation completed!" -ForegroundColor Green
        
    } catch {
        Write-Host "Error installing ERPNext: $_" -ForegroundColor Red
        exit 1
    }
}

# Phase 4: Configure auto-start and create shortcuts
Write-Host "`n=== Phase 4: Configuring Auto-start and Shortcuts ===" -ForegroundColor Cyan

try {
    # Create Windows batch files
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    
    @"
@echo off
echo Starting ERPNext Development Environment...
wsl -d Ubuntu-22.04 -- sudo service mariadb start
wsl -d Ubuntu-22.04 -- sudo service redis-server start
echo Services started. Starting ERPNext...
wsl -d Ubuntu-22.04 -- sudo -u frappeuser bash -c "cd /home/<USER>/frappe-bench && ~/.local/bin/bench start"
"@ | Out-File -FilePath "$desktopPath\Start_ERPNext.bat" -Encoding ASCII
    
    @"
@echo off
echo Stopping ERPNext Development Environment...
wsl -d Ubuntu-22.04 -- sudo pkill -f "bench start"
wsl -d Ubuntu-22.04 -- sudo service redis-server stop
wsl -d Ubuntu-22.04 -- sudo service mariadb stop
echo ERPNext services stopped.
pause
"@ | Out-File -FilePath "$desktopPath\Stop_ERPNext.bat" -Encoding ASCII
    
    @"
@echo off
echo Opening ERPNext in browser...
start http://localhost:8000
"@ | Out-File -FilePath "$desktopPath\Open_ERPNext.bat" -Encoding ASCII
    
    Write-Host "Shortcuts created on Desktop!" -ForegroundColor Green
    
} catch {
    Write-Host "Warning: Could not create desktop shortcuts: $_" -ForegroundColor Yellow
}

# Final summary
Write-Host "`n=== Installation Summary ===" -ForegroundColor Green
Write-Host "ERPNext Version 15 has been successfully installed!" -ForegroundColor Green
Write-Host ""
Write-Host "Access Information:" -ForegroundColor Cyan
Write-Host "- URL: http://localhost:8000" -ForegroundColor White
Write-Host "- Username: Administrator" -ForegroundColor White
Write-Host "- Password: $AdminPassword" -ForegroundColor White
Write-Host ""
Write-Host "Desktop Shortcuts Created:" -ForegroundColor Cyan
Write-Host "- Start_ERPNext.bat: Start the development server" -ForegroundColor White
Write-Host "- Stop_ERPNext.bat: Stop all services" -ForegroundColor White
Write-Host "- Open_ERPNext.bat: Open ERPNext in browser" -ForegroundColor White
Write-Host ""
Write-Host "To start ERPNext now, run: Start_ERPNext.bat" -ForegroundColor Yellow
Write-Host "Installation completed successfully!" -ForegroundColor Green

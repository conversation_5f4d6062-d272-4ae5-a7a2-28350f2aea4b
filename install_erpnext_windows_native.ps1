# ERPNext Native Windows Installation Script
# This script installs ERPNext to run natively on Windows 11 without WSL

param(
    [string]$InstallPath = "C:\ERPNext",
    [string]$MariaDBPassword = "admin123",
    [string]$AdminPassword = "admin123",
    [switch]$SkipDependencies,
    [switch]$SkipERPNext,
    [switch]$InstallAsService
)

$ErrorActionPreference = "Stop"

Write-Host "=== ERPNext Native Windows Installation ===" -ForegroundColor Green
Write-Host "Installing ERPNext to run natively on Windows 11" -ForegroundColor Yellow

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run PowerShell as Administrator." -ForegroundColor Red
    exit 1
}

# Create installation directory
New-Item -ItemType Directory -Force -Path $InstallPath | Out-Null
Set-Location $InstallPath

Write-Host "Installation directory: $InstallPath" -ForegroundColor Cyan

# Phase 1: Install Dependencies
if (-not $SkipDependencies) {
    Write-Host "`n=== Phase 1: Installing Dependencies ===" -ForegroundColor Cyan
    
    # Install Chocolatey if not present
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "Installing Chocolatey package manager..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        refreshenv
    }
    
    # Install Python 3.10+
    Write-Host "Installing Python 3.10..." -ForegroundColor Yellow
    choco install python310 -y
    refreshenv
    
    # Install Git
    Write-Host "Installing Git..." -ForegroundColor Yellow
    choco install git -y
    
    # Install Node.js 18
    Write-Host "Installing Node.js 18..." -ForegroundColor Yellow
    choco install nodejs --version=18.20.4 -y
    
    # Install Yarn
    Write-Host "Installing Yarn..." -ForegroundColor Yellow
    npm install -g yarn
    
    # Install MariaDB
    Write-Host "Installing MariaDB..." -ForegroundColor Yellow
    choco install mariadb -y
    
    # Install Redis
    Write-Host "Installing Redis..." -ForegroundColor Yellow
    choco install redis-64 -y
    
    # Install wkhtmltopdf
    Write-Host "Installing wkhtmltopdf..." -ForegroundColor Yellow
    choco install wkhtmltopdf -y
    
    # Install Visual C++ Build Tools (required for some Python packages)
    Write-Host "Installing Visual C++ Build Tools..." -ForegroundColor Yellow
    choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools" -y
    
    Write-Host "Dependencies installation completed!" -ForegroundColor Green
}

# Phase 2: Configure Services
Write-Host "`n=== Phase 2: Configuring Services ===" -ForegroundColor Cyan

# Configure MariaDB
Write-Host "Configuring MariaDB..." -ForegroundColor Yellow
try {
    # Start MariaDB service
    Start-Service -Name "MySQL" -ErrorAction SilentlyContinue
    
    # Set root password and create database
    $mysqlPath = (Get-Command mysql -ErrorAction SilentlyContinue).Source
    if ($mysqlPath) {
        & mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MariaDBPassword';"
        & mysql -u root -p$MariaDBPassword -e "CREATE DATABASE IF NOT EXISTS erpnext_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        & mysql -u root -p$MariaDBPassword -e "FLUSH PRIVILEGES;"
        Write-Host "MariaDB configured successfully" -ForegroundColor Green
    } else {
        Write-Host "Warning: MySQL command not found in PATH" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Warning: MariaDB configuration failed: $_" -ForegroundColor Yellow
}

# Configure Redis
Write-Host "Configuring Redis..." -ForegroundColor Yellow
try {
    Start-Service -Name "Redis" -ErrorAction SilentlyContinue
    Write-Host "Redis configured successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Redis service not found or failed to start" -ForegroundColor Yellow
}

# Phase 3: Install ERPNext
if (-not $SkipERPNext) {
    Write-Host "`n=== Phase 3: Installing ERPNext ===" -ForegroundColor Cyan
    
    # Create bench directory
    $benchPath = Join-Path $InstallPath "frappe-bench"
    New-Item -ItemType Directory -Force -Path $benchPath | Out-Null
    Set-Location $benchPath
    
    # Copy Windows bench CLI
    $sourcePath = Split-Path -Parent $MyInvocation.MyCommand.Path
    Copy-Item "$sourcePath\windows_bench.py" $benchPath -Force
    Copy-Item "$sourcePath\windows_service_manager.py" $benchPath -Force
    
    # Create Python virtual environment
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv env
    
    # Activate virtual environment
    $activateScript = Join-Path $benchPath "env\Scripts\Activate.ps1"
    & $activateScript
    
    # Upgrade pip
    python -m pip install --upgrade pip
    
    # Install required Python packages
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    pip install wheel setuptools
    pip install mysql-connector-python
    pip install redis
    pip install psutil
    pip install pywin32
    
    # Initialize bench using Windows bench CLI
    Write-Host "Initializing Frappe bench..." -ForegroundColor Yellow
    python windows_bench.py init frappe-bench --frappe-branch version-15
    
    Set-Location $benchPath
    
    # Get ERPNext app
    Write-Host "Getting ERPNext application..." -ForegroundColor Yellow
    python windows_bench.py get-app erpnext --branch version-15
    
    # Create new site
    Write-Host "Creating new site..." -ForegroundColor Yellow
    python windows_bench.py new-site site1.local --admin-password $AdminPassword --mariadb-root-password $MariaDBPassword
    
    # Install ERPNext on site
    Write-Host "Installing ERPNext on site..." -ForegroundColor Yellow
    python windows_bench.py install-app erpnext --site site1.local
    
    Write-Host "ERPNext installation completed!" -ForegroundColor Green
}

# Phase 4: Configure Windows Services
if ($InstallAsService) {
    Write-Host "`n=== Phase 4: Installing Windows Services ===" -ForegroundColor Cyan
    
    # Install ERPNext as Windows service
    Write-Host "Installing ERPNext as Windows service..." -ForegroundColor Yellow
    python windows_service_manager.py install --bench-path $benchPath
    
    # Start all services
    Write-Host "Starting all services..." -ForegroundColor Yellow
    python windows_service_manager.py start-all
}

# Phase 5: Create Desktop Shortcuts
Write-Host "`n=== Phase 5: Creating Desktop Shortcuts ===" -ForegroundColor Cyan

$desktopPath = [Environment]::GetFolderPath("Desktop")

# Create Start ERPNext shortcut
$startScript = @"
@echo off
cd /d "$benchPath"
echo Starting ERPNext Development Server...
echo ERPNext will be available at http://localhost:8000
echo Press Ctrl+C to stop the server
env\Scripts\python.exe windows_bench.py start
pause
"@
$startScript | Out-File -FilePath "$desktopPath\Start_ERPNext_Native.bat" -Encoding ASCII

# Create Stop ERPNext shortcut
$stopScript = @"
@echo off
echo Stopping ERPNext services...
cd /d "$benchPath"
env\Scripts\python.exe windows_service_manager.py stop-all
echo ERPNext services stopped.
pause
"@
$stopScript | Out-File -FilePath "$desktopPath\Stop_ERPNext_Native.bat" -Encoding ASCII

# Create ERPNext Status shortcut
$statusScript = @"
@echo off
cd /d "$benchPath"
echo Checking ERPNext status...
env\Scripts\python.exe windows_service_manager.py status
pause
"@
$statusScript | Out-File -FilePath "$desktopPath\ERPNext_Status.bat" -Encoding ASCII

# Create Open ERPNext shortcut
$openScript = @"
@echo off
echo Opening ERPNext in browser...
start http://localhost:8000
"@
$openScript | Out-File -FilePath "$desktopPath\Open_ERPNext.bat" -Encoding ASCII

Write-Host "Desktop shortcuts created!" -ForegroundColor Green

# Phase 6: Create configuration file
Write-Host "`n=== Phase 6: Creating Configuration ===" -ForegroundColor Cyan

$configFile = @{
    "installation_path" = $InstallPath
    "bench_path" = $benchPath
    "mariadb_password" = $MariaDBPassword
    "admin_password" = $AdminPassword
    "site_name" = "site1.local"
    "installation_date" = (Get-Date).ToString()
    "version" = "15.x.x-develop"
    "windows_native" = $true
}

$configFile | ConvertTo-Json -Depth 3 | Out-File -FilePath "$InstallPath\erpnext_config.json" -Encoding UTF8

# Final summary
Write-Host "`n=== Installation Summary ===" -ForegroundColor Green
Write-Host "ERPNext has been successfully installed natively on Windows!" -ForegroundColor Green
Write-Host ""
Write-Host "Installation Details:" -ForegroundColor Cyan
Write-Host "- Installation Path: $InstallPath" -ForegroundColor White
Write-Host "- Bench Path: $benchPath" -ForegroundColor White
Write-Host "- Site Name: site1.local" -ForegroundColor White
Write-Host ""
Write-Host "Access Information:" -ForegroundColor Cyan
Write-Host "- URL: http://localhost:8000" -ForegroundColor White
Write-Host "- Username: Administrator" -ForegroundColor White
Write-Host "- Password: $AdminPassword" -ForegroundColor White
Write-Host ""
Write-Host "Desktop Shortcuts Created:" -ForegroundColor Cyan
Write-Host "- Start_ERPNext_Native.bat: Start ERPNext development server" -ForegroundColor White
Write-Host "- Stop_ERPNext_Native.bat: Stop all ERPNext services" -ForegroundColor White
Write-Host "- ERPNext_Status.bat: Check service status" -ForegroundColor White
Write-Host "- Open_ERPNext.bat: Open ERPNext in browser" -ForegroundColor White
Write-Host ""

if ($InstallAsService) {
    Write-Host "Windows Services:" -ForegroundColor Cyan
    Write-Host "- ERPNext is installed as a Windows service" -ForegroundColor White
    Write-Host "- Services will start automatically on system boot" -ForegroundColor White
    Write-Host ""
}

Write-Host "To start ERPNext now:" -ForegroundColor Yellow
if ($InstallAsService) {
    Write-Host "- Services are already running" -ForegroundColor White
    Write-Host "- Open http://localhost:8000 in your browser" -ForegroundColor White
} else {
    Write-Host "- Double-click Start_ERPNext_Native.bat on your desktop" -ForegroundColor White
    Write-Host "- Or run: cd '$benchPath' && env\Scripts\python.exe windows_bench.py start" -ForegroundColor White
}

Write-Host ""
Write-Host "Installation completed successfully! 🎉" -ForegroundColor Green

# ERPNext Native Windows Installation

This repository contains a complete Windows-native implementation of ERPNext that runs without WSL, Docker, or any Linux emulation. ERPNext runs directly on Windows 11 using native Windows tools and services.

## 🎯 **What's Different**

### **Traditional ERPNext Installation**
- Requires Linux or WSL2
- Uses Frappe Bench CLI (Linux-based)
- Relies on systemd for service management
- Uses Unix-style paths and permissions
- Requires bash scripts

### **This Windows-Native Implementation**
- ✅ Runs directly on Windows 11
- ✅ Custom Windows Bench CLI replacement
- ✅ Windows Services for auto-start
- ✅ Windows-native paths and file handling
- ✅ PowerShell and Batch scripts
- ✅ No WSL or Linux dependencies

## 🚀 **Quick Start**

### **One-Click Installation**
1. **Download** all files to a folder
2. **Right-click** `Install_ERPNext_Windows_Native.bat`
3. **Select** "Run as administrator"
4. **Follow** the installation prompts
5. **Access** ERPNext at http://localhost:8000

### **What Gets Installed**
- Python 3.10+ with virtual environment
- Node.js 18+ with Yarn
- MariaDB database server
- Redis cache server
- wkhtmltopdf for PDF generation
- Visual C++ Build Tools
- Frappe Framework v15
- ERPNext v15
- Windows-native management tools

## 📁 **File Structure**

```
ERPNext-Windows-Native/
├── Install_ERPNext_Windows_Native.bat     # Main installation script
├── install_erpnext_windows_native.ps1     # PowerShell installer
├── windows_bench.py                       # Windows Bench CLI replacement
├── windows_service_manager.py             # Windows service management
├── windows_process_manager.py             # Process management (Procfile replacement)
├── requirements_windows.txt               # Windows-specific Python requirements
├── README_WINDOWS_NATIVE.md              # This file
└── Installation Output/
    ├── C:\ERPNext\                        # Default installation directory
    ├── frappe-bench\                      # Bench directory
    ├── env\                               # Python virtual environment
    ├── apps\                              # Frappe apps (frappe, erpnext)
    ├── sites\                             # ERPNext sites
    └── logs\                              # Log files
```

## 🔧 **Core Components**

### **1. Windows Bench CLI (`windows_bench.py`)**
Replaces the Linux-based Frappe Bench CLI with Windows-native functionality:

```bash
# Traditional bench commands
bench init frappe-bench
bench get-app erpnext
bench new-site site1.local
bench start

# Windows-native equivalent
python windows_bench.py init frappe-bench
python windows_bench.py get-app erpnext
python windows_bench.py new-site site1.local
python windows_bench.py start
```

**Features:**
- Git repository management
- Virtual environment handling
- Site creation and management
- App installation
- Development server startup

### **2. Windows Service Manager (`windows_service_manager.py`)**
Manages ERPNext as Windows services instead of systemd:

```bash
# Install ERPNext as Windows service
python windows_service_manager.py install --bench-path C:\ERPNext\frappe-bench

# Service management
python windows_service_manager.py start-all
python windows_service_manager.py stop-all
python windows_service_manager.py status
```

**Features:**
- Windows service installation/removal
- Auto-start on system boot
- Service status monitoring
- Dependency checking (MariaDB, Redis)

### **3. Process Manager (`windows_process_manager.py`)**
Replaces Procfile functionality for Windows:

```bash
# Start all processes (web, worker, scheduler, socketio)
python windows_process_manager.py start

# Manage specific processes
python windows_process_manager.py start --process web
python windows_process_manager.py stop --process worker
python windows_process_manager.py logs --process web
```

**Features:**
- Multi-process management
- Automatic restart on failure
- Log file management
- Process monitoring

## 🛠 **Installation Options**

### **Option 1: Complete Installation (Recommended)**
```batch
Install_ERPNext_Windows_Native.bat
# Choose option 1
```
- Installs all dependencies
- Sets up ERPNext
- Creates desktop shortcuts

### **Option 2: With Windows Services**
```batch
Install_ERPNext_Windows_Native.bat
# Choose option 2
```
- Everything in option 1
- Installs as Windows service
- Auto-starts on boot

### **Option 3: Dependencies Only**
```batch
Install_ERPNext_Windows_Native.bat
# Choose option 3
```
- Only installs Python, Node.js, MariaDB, Redis
- Skips ERPNext installation

### **Option 4: ERPNext Only**
```batch
Install_ERPNext_Windows_Native.bat
# Choose option 4
```
- Assumes dependencies are installed
- Only sets up ERPNext

### **Option 5: Custom Path**
```batch
Install_ERPNext_Windows_Native.bat
# Choose option 5
# Enter custom installation path
```

## 🎮 **Usage**

### **Starting ERPNext**

**Method 1: Desktop Shortcuts**
- Double-click `Start_ERPNext_Native.bat`

**Method 2: Command Line**
```cmd
cd C:\ERPNext\frappe-bench
env\Scripts\python.exe windows_bench.py start
```

**Method 3: Windows Service** (if installed)
```cmd
net start ERPNextService
```

### **Stopping ERPNext**

**Method 1: Desktop Shortcut**
- Double-click `Stop_ERPNext_Native.bat`

**Method 2: Command Line**
```cmd
cd C:\ERPNext\frappe-bench
env\Scripts\python.exe windows_process_manager.py stop
```

**Method 3: Windows Service**
```cmd
net stop ERPNextService
```

### **Checking Status**
- Double-click `ERPNext_Status.bat`
- Or run: `python windows_service_manager.py status`

## 🔍 **Key Differences from Linux Version**

### **Path Handling**
```python
# Linux/WSL
/home/<USER>/frappe-bench/sites/site1.local

# Windows Native
C:\ERPNext\frappe-bench\sites\site1.local
```

### **Service Management**
```bash
# Linux
sudo systemctl start mariadb
sudo systemctl start redis-server

# Windows
net start MySQL
net start Redis
```

### **Process Management**
```bash
# Linux (Procfile)
web: bench serve --port 8000
worker: bench worker

# Windows (windows_process_manager.py)
"web": [python, "-m", "frappe", "serve", "--port", "8000"]
"worker": [python, "-m", "frappe", "worker"]
```

### **Virtual Environment**
```bash
# Linux
source env/bin/activate

# Windows
env\Scripts\activate.bat
```

## 🐛 **Troubleshooting**

### **Common Issues**

**1. Port 8000 Already in Use**
```cmd
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

**2. MariaDB Connection Failed**
```cmd
# Check if MariaDB service is running
net start MySQL

# Test connection
mysql -u root -p
```

**3. Redis Connection Failed**
```cmd
# Check if Redis service is running
net start Redis

# Test connection
redis-cli ping
```

**4. Python Module Not Found**
```cmd
cd C:\ERPNext\frappe-bench
env\Scripts\pip install -r requirements_windows.txt
```

### **Log Files**
- **Installation**: `C:\ERPNext\installation.log`
- **ERPNext Web**: `C:\ERPNext\frappe-bench\logs\web.log`
- **Worker**: `C:\ERPNext\frappe-bench\logs\worker.log`
- **Scheduler**: `C:\ERPNext\frappe-bench\logs\schedule.log`
- **Process Manager**: `C:\ERPNext\frappe-bench\logs\process_manager.log`

### **Service Status**
```cmd
# Check all ERPNext services
python windows_service_manager.py status

# Check Windows services
sc query MySQL
sc query Redis
sc query ERPNextService
```

## 🔧 **Advanced Configuration**

### **Custom Database Settings**
Edit `C:\ERPNext\frappe-bench\sites\site1.local\site_config.json`:
```json
{
  "db_host": "localhost",
  "db_port": 3306,
  "db_name": "site1_local",
  "db_password": "your_password"
}
```

### **Custom Redis Settings**
Edit `C:\ERPNext\frappe-bench\sites\common_site_config.json`:
```json
{
  "redis_cache": "redis://localhost:6379",
  "redis_queue": "redis://localhost:6379/1",
  "redis_socketio": "redis://localhost:6379/2"
}
```

### **Performance Tuning**
```json
{
  "background_workers": 2,
  "gunicorn_workers": 4,
  "webserver_port": 8000
}
```

## 📊 **System Requirements**

### **Minimum Requirements**
- Windows 11 (or Windows 10 version 2004+)
- 8GB RAM
- 20GB free disk space
- Administrator privileges
- Internet connection

### **Recommended Requirements**
- Windows 11
- 16GB RAM
- 50GB free disk space (SSD preferred)
- Stable internet connection

## 🆘 **Support**

### **Getting Help**
- **Documentation**: https://docs.erpnext.com/
- **Community Forum**: https://discuss.erpnext.com/
- **GitHub Issues**: https://github.com/frappe/erpnext/issues

### **Windows-Specific Issues**
- Check Windows Event Viewer for service errors
- Verify Windows Defender/antivirus isn't blocking files
- Ensure all required Windows features are enabled
- Check Windows firewall settings for port 8000

## 🎉 **Success Indicators**

### **Installation Complete When:**
✅ No error messages in installation output  
✅ Desktop shortcuts created  
✅ ERPNext accessible at http://localhost:8000  
✅ Can log in with Administrator/admin123  
✅ All services show "Running" status  

### **Ready for Production When:**
✅ Windows services installed and auto-starting  
✅ Database backups configured  
✅ SSL certificate installed  
✅ Domain name configured  
✅ Email settings configured  

---

**Congratulations! You now have ERPNext running natively on Windows 11! 🎉**

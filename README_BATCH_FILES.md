# ERPNext Installation Batch Files Guide

This directory contains easy-to-use batch files for installing and managing ERPNext Version 15 on Windows 11 with WSL2.

## 🚀 Quick Start

### **For Most Users (Recommended)**
1. **Right-click** on `Install_ERPNext.bat`
2. Select **"Run as administrator"**
3. Follow the prompts

## 📁 Batch Files Overview

### 🎯 **Main Installation Files**

#### `Install_ERPNext.bat` ⭐ **RECOMMENDED**
- **Purpose**: Complete automated installation
- **What it does**: Installs WSL2, dependencies, and ERPNext in one go
- **Time**: 30-60 minutes
- **Requirements**: Administrator privileges

#### `Install_ERPNext_Advanced.bat`
- **Purpose**: Step-by-step installation with options
- **What it does**: Provides menu-driven installation
- **Use when**: You want more control over the process
- **Features**:
  - Install components separately
  - Skip already completed steps
  - Access troubleshooting tools
  - View installation guide

### 🛠 **Management and Troubleshooting**

#### `Troubleshoot_ERPNext.bat`
- **Purpose**: Diagnose and fix common issues
- **What it does**: 
  - Check service status
  - Restart services
  - Fix permissions
  - View logs
  - Test connections
- **Use when**: ERPNext is not working properly

## 🎯 **Installation Process**

### **Step 1: Choose Your Installation Method**

**Option A: Simple Installation (Recommended)**
```
Right-click Install_ERPNext.bat → Run as administrator
```

**Option B: Advanced Installation**
```
Right-click Install_ERPNext_Advanced.bat → Run as administrator
```

### **Step 2: Follow the Prompts**

The batch files will guide you through:
1. WSL2 installation (if needed)
2. Ubuntu 22.04 setup
3. Dependency installation
4. ERPNext configuration
5. Service setup

### **Step 3: Access ERPNext**

After installation:
- **URL**: http://localhost:8000
- **Username**: Administrator
- **Password**: admin123

## 🖥 **Desktop Shortcuts Created**

After successful installation, these shortcuts will appear on your desktop:

- **`Start_ERPNext.bat`** - Start ERPNext development server
- **`Stop_ERPNext.bat`** - Stop all ERPNext services
- **`Open_ERPNext.bat`** - Open ERPNext in your browser

## ⚠️ **Important Requirements**

### **Before Running Any Batch File:**

1. **Administrator Privileges Required**
   - Right-click the batch file
   - Select "Run as administrator"
   - This is mandatory for WSL2 installation

2. **System Requirements**
   - Windows 11 (Windows 10 version 2004+ also works)
   - At least 8GB RAM (16GB recommended)
   - At least 20GB free disk space
   - Stable internet connection

3. **Antivirus Considerations**
   - Some antivirus software may block WSL2 installation
   - Temporarily disable real-time protection if needed
   - Add the installation directory to antivirus exclusions

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: "Access Denied" or "Permission Error"**
**Solution**: Make sure you're running as Administrator
```
Right-click batch file → Run as administrator
```

### **Issue 2: PowerShell Execution Policy Error**
**Solution**: The batch files handle this automatically, but if you see errors:
```
powershell -ExecutionPolicy Bypass -File install_erpnext_complete.ps1
```

### **Issue 3: WSL2 Installation Fails**
**Solutions**:
1. Enable Windows features:
   - Windows Subsystem for Linux
   - Virtual Machine Platform
2. Update Windows to latest version
3. Restart computer and try again

### **Issue 4: ERPNext Won't Start**
**Solution**: Use the troubleshooting batch file
```
Right-click Troubleshoot_ERPNext.bat → Run as administrator
```

### **Issue 5: Port 8000 Already in Use**
**Solution**: 
1. Run `Troubleshoot_ERPNext.bat`
2. Choose option 7 (Kill Stuck Processes)
3. Try starting ERPNext again

## 📋 **Installation Steps Breakdown**

### **What Each Phase Does:**

1. **Phase 1: WSL2 Setup**
   - Installs Windows Subsystem for Linux 2
   - Downloads and installs Ubuntu 22.04
   - May require system restart

2. **Phase 2: Dependencies**
   - Updates Ubuntu packages
   - Installs Python 3.10+, Node.js 18, Git
   - Installs MariaDB, Redis, wkhtmltopdf
   - Creates frappeuser with sudo privileges

3. **Phase 3: Database Configuration**
   - Configures MariaDB for UTF-8 support
   - Sets up Redis for caching
   - Secures database installation

4. **Phase 4: ERPNext Installation**
   - Installs Frappe Bench CLI
   - Initializes Frappe bench with version-15
   - Creates new site (site1.local)
   - Fetches and installs ERPNext v15

5. **Phase 5: Final Configuration**
   - Sets up auto-start services
   - Creates desktop shortcuts
   - Configures development environment

## 🆘 **Getting Help**

### **If Installation Fails:**

1. **Check the error messages** in the command window
2. **Run the troubleshooting tool**:
   ```
   Troubleshoot_ERPNext.bat → Option 8 (Complete System Check)
   ```
3. **Check the installation guide**: `ERPNEXT_INSTALLATION_GUIDE.md`
4. **Common solutions**:
   - Restart computer and try again
   - Disable antivirus temporarily
   - Ensure stable internet connection
   - Free up disk space (need 20GB+)

### **For Advanced Users:**

- **Manual installation**: Follow `ERPNEXT_INSTALLATION_GUIDE.md`
- **PowerShell scripts**: Run `.ps1` files directly
- **WSL commands**: Access Ubuntu directly via `wsl -d Ubuntu-22.04`

## 🎉 **Success Indicators**

### **Installation is Complete When:**

✅ No error messages in the final output  
✅ Desktop shortcuts are created  
✅ ERPNext opens at http://localhost:8000  
✅ You can log in with Administrator/admin123  

### **Services are Running When:**

✅ MariaDB service is active  
✅ Redis service is active  
✅ ERPNext development server is running  
✅ Port 8000 is accessible  

## 📞 **Support Resources**

- **ERPNext Documentation**: https://docs.erpnext.com/
- **Frappe Framework**: https://frappeframework.com/
- **Community Forum**: https://discuss.erpnext.com/
- **GitHub Issues**: https://github.com/frappe/erpnext/issues

---

**Happy ERPNext Installation! 🚀**

@echo off
REM ERPNext Troubleshooting Batch File

echo ===============================================
echo    ERPNext Troubleshooting Tool
echo ===============================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges... Good!
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please right-click on this batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:MENU
echo ===============================================
echo    Troubleshooting Options
echo ===============================================
echo.
echo 1. Check All Services and Status
echo 2. Restart All Services
echo 3. Check Port Usage
echo 4. Fix Permissions
echo 5. View Recent Logs
echo 6. Test Database Connection
echo 7. Kill Stuck Processes
echo 8. Complete System Check
echo 9. Return to Main Menu
echo 0. Exit
echo.
set /p "choice=Please select an option (0-9): "

if "%choice%"=="1" goto CHECK_SERVICES
if "%choice%"=="2" goto RESTART_SERVICES
if "%choice%"=="3" goto CHECK_PORTS
if "%choice%"=="4" goto FIX_PERMISSIONS
if "%choice%"=="5" goto VIEW_LOGS
if "%choice%"=="6" goto TEST_DB
if "%choice%"=="7" goto KILL_PROCESSES
if "%choice%"=="8" goto COMPLETE_CHECK
if "%choice%"=="9" goto MAIN_MENU
if "%choice%"=="0" goto EXIT
echo Invalid choice. Please try again.
echo.
goto MENU

:CHECK_SERVICES
echo.
echo Checking service status...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -CheckServices
goto CONTINUE

:RESTART_SERVICES
echo.
echo Restarting all services...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -RestartServices
goto CONTINUE

:CHECK_PORTS
echo.
echo Checking port usage...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -CheckPorts
goto CONTINUE

:FIX_PERMISSIONS
echo.
echo Fixing permissions...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -FixPermissions
goto CONTINUE

:VIEW_LOGS
echo.
echo Viewing recent logs...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -ViewLogs
goto CONTINUE

:TEST_DB
echo.
echo Testing database connection...
echo Checking MariaDB connection...
wsl -d Ubuntu-22.04 -- sudo mysql -u root -padmin123 -e "SHOW DATABASES;" 2>nul
if %errorLevel% == 0 (
    echo Database connection: OK
) else (
    echo Database connection: FAILED
    echo Try restarting MariaDB service
)
goto CONTINUE

:KILL_PROCESSES
echo.
echo Killing stuck ERPNext processes...
wsl -d Ubuntu-22.04 -- sudo pkill -f "bench start" 2>nul
wsl -d Ubuntu-22.04 -- sudo pkill -f "frappe" 2>nul
echo Processes killed. You can now restart ERPNext.
goto CONTINUE

:COMPLETE_CHECK
echo.
echo Running complete system check...
powershell.exe -ExecutionPolicy Bypass -File "troubleshoot_erpnext.ps1" -All
goto CONTINUE

:MAIN_MENU
if exist "Install_ERPNext_Advanced.bat" (
    echo.
    echo Launching main installation menu...
    call "Install_ERPNext_Advanced.bat"
) else (
    echo Main menu not found!
    goto CONTINUE
)
goto EXIT

:CONTINUE
echo.
echo ===============================================
echo    Quick Actions
echo ===============================================
echo.
echo A. Start ERPNext (if services are running)
echo B. Open ERPNext in browser
echo C. Return to troubleshooting menu
echo D. Exit
echo.
set /p "action=Select an action (A-D): "

if /i "%action%"=="A" goto START_ERPNEXT
if /i "%action%"=="B" goto OPEN_BROWSER
if /i "%action%"=="C" goto MENU
if /i "%action%"=="D" goto EXIT
echo Invalid choice.
goto CONTINUE

:START_ERPNEXT
echo.
echo Starting ERPNext development server...
echo This will open in a new window. Close this window to stop ERPNext.
start cmd /k "wsl -d Ubuntu-22.04 -- sudo -u frappeuser bash -c 'cd /home/<USER>/frappe-bench && ~/.local/bin/bench start'"
goto CONTINUE

:OPEN_BROWSER
echo.
echo Opening ERPNext in browser...
start http://localhost:8000
goto CONTINUE

:EXIT
echo.
echo Troubleshooting session ended.
echo.
echo If you need further help:
echo - Check the installation guide: ERPNEXT_INSTALLATION_GUIDE.md
echo - Visit ERPNext community: https://discuss.erpnext.com/
echo.
echo Press any key to exit...
pause >nul

#!/usr/bin/env python3
"""
Windows-Native Frappe Bench CLI Replacement
This module provides Windows-compatible bench commands for ERPNext
"""

import os
import sys
import json
import subprocess
import argparse
import shutil
import time
from pathlib import Path
import configparser
import threading
import signal
import psutil

class WindowsBench:
    def __init__(self, bench_path=None):
        self.bench_path = Path(bench_path) if bench_path else Path.cwd()
        self.sites_path = self.bench_path / "sites"
        self.apps_path = self.bench_path / "apps"
        self.config_path = self.bench_path / "config"
        self.logs_path = self.bench_path / "logs"
        
        # Ensure directories exist
        for path in [self.sites_path, self.apps_path, self.config_path, self.logs_path]:
            path.mkdir(exist_ok=True)
    
    def init_bench(self, frappe_branch="version-15", python_path=None):
        """Initialize a new bench directory"""
        print(f"Initializing bench at {self.bench_path}")
        
        # Create directory structure
        self.bench_path.mkdir(exist_ok=True)
        os.chdir(self.bench_path)
        
        # Clone Frappe framework
        frappe_path = self.apps_path / "frappe"
        if not frappe_path.exists():
            print("Cloning Frappe framework...")
            subprocess.run([
                "git", "clone", "--branch", frappe_branch,
                "https://github.com/frappe/frappe.git",
                str(frappe_path)
            ], check=True)
        
        # Create virtual environment
        venv_path = self.bench_path / "env"
        if not venv_path.exists():
            print("Creating virtual environment...")
            python_exe = python_path or sys.executable
            subprocess.run([python_exe, "-m", "venv", str(venv_path)], check=True)
        
        # Install Frappe
        self._install_app("frappe", str(frappe_path))
        
        # Create common_site_config.json
        self._create_common_site_config()
        
        # Create Procfile equivalent
        self._create_procfile()
        
        print("Bench initialization completed!")
    
    def get_app(self, app_name, branch=None, git_url=None):
        """Get an app from git repository"""
        if git_url is None:
            git_url = f"https://github.com/frappe/{app_name}.git"
        
        app_path = self.apps_path / app_name
        if app_path.exists():
            print(f"App {app_name} already exists")
            return
        
        print(f"Getting app {app_name}...")
        cmd = ["git", "clone"]
        if branch:
            cmd.extend(["--branch", branch])
        cmd.extend([git_url, str(app_path)])
        
        subprocess.run(cmd, check=True)
        self._install_app(app_name, str(app_path))
        print(f"App {app_name} installed successfully!")
    
    def new_site(self, site_name, admin_password="admin", db_root_password="root"):
        """Create a new site"""
        site_path = self.sites_path / site_name
        site_path.mkdir(exist_ok=True)
        
        print(f"Creating site {site_name}...")
        
        # Create site_config.json
        site_config = {
            "db_name": site_name.replace(".", "_"),
            "db_password": admin_password,
            "admin_password": admin_password,
            "host_name": f"http://{site_name}:8000",
            "auto_email_id": "<EMAIL>",
            "mail_server": "localhost",
            "use_ssl": 0,
            "developer_mode": 1
        }
        
        with open(site_path / "site_config.json", "w") as f:
            json.dump(site_config, f, indent=2)
        
        # Create database
        self._create_database(site_name, admin_password, db_root_password)
        
        # Install frappe app
        self._run_frappe_command(site_name, ["install-app", "frappe"])
        
        print(f"Site {site_name} created successfully!")
    
    def install_app(self, site_name, app_name):
        """Install an app on a site"""
        print(f"Installing {app_name} on {site_name}...")
        self._run_frappe_command(site_name, ["install-app", app_name])
        print(f"App {app_name} installed on {site_name}!")
    
    def start(self, port=8000):
        """Start the development server"""
        print("Starting ERPNext development server...")
        
        # Start Redis if not running
        self._start_redis()
        
        # Start the web server
        self._start_web_server(port)
    
    def _install_app(self, app_name, app_path):
        """Install an app using pip"""
        venv_python = self.bench_path / "env" / "Scripts" / "python.exe"
        subprocess.run([
            str(venv_python), "-m", "pip", "install", "-e", app_path
        ], check=True)
    
    def _create_common_site_config(self):
        """Create common_site_config.json"""
        config = {
            "background_workers": 1,
            "file_watcher_port": 6787,
            "frappe_user": os.getenv("USERNAME", "Administrator"),
            "gunicorn_workers": 4,
            "live_reload": True,
            "rebase_on_pull": False,
            "redis_cache": "redis://localhost:6379",
            "redis_queue": "redis://localhost:6379/1",
            "redis_socketio": "redis://localhost:6379/2",
            "restart_supervisor_on_update": False,
            "restart_systemd_on_update": False,
            "serve_default_site": True,
            "shallow_clone": True,
            "socketio_port": 9000,
            "use_redis_auth": False,
            "webserver_port": 8000
        }
        
        with open(self.sites_path / "common_site_config.json", "w") as f:
            json.dump(config, f, indent=2)
    
    def _create_procfile(self):
        """Create Procfile equivalent for Windows"""
        procfile_content = """# Windows Procfile for ERPNext
web: python -m frappe serve --port 8000 --noreload
worker: python -m frappe worker
schedule: python -m frappe schedule
socketio: python -m frappe socketio
"""
        with open(self.bench_path / "Procfile", "w") as f:
            f.write(procfile_content)
    
    def _create_database(self, site_name, admin_password, db_root_password):
        """Create database for the site"""
        db_name = site_name.replace(".", "_")
        
        # Connect to MariaDB and create database
        try:
            import mysql.connector
            conn = mysql.connector.connect(
                host="localhost",
                user="root",
                password=db_root_password
            )
            cursor = conn.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            cursor.execute(f"GRANT ALL PRIVILEGES ON `{db_name}`.* TO 'root'@'localhost'")
            cursor.execute("FLUSH PRIVILEGES")
            conn.close()
            print(f"Database {db_name} created successfully")
        except Exception as e:
            print(f"Error creating database: {e}")
            print("Please ensure MariaDB is running and accessible")
    
    def _run_frappe_command(self, site_name, command):
        """Run a frappe command for a specific site"""
        venv_python = self.bench_path / "env" / "Scripts" / "python.exe"
        cmd = [str(venv_python), "-m", "frappe", "--site", site_name] + command
        
        env = os.environ.copy()
        env["PYTHONPATH"] = str(self.bench_path)
        
        subprocess.run(cmd, cwd=str(self.bench_path), env=env, check=True)
    
    def _start_redis(self):
        """Start Redis server if not running"""
        try:
            # Check if Redis is running
            subprocess.run(["redis-cli", "ping"], 
                         capture_output=True, check=True, timeout=5)
            print("Redis is already running")
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
            print("Starting Redis server...")
            try:
                # Try to start Redis as a Windows service
                subprocess.run(["net", "start", "Redis"], check=True)
            except subprocess.CalledProcessError:
                # If service doesn't exist, try to start Redis directly
                try:
                    subprocess.Popen(["redis-server"], 
                                   creationflags=subprocess.CREATE_NEW_CONSOLE)
                    time.sleep(2)  # Give Redis time to start
                except FileNotFoundError:
                    print("Redis not found. Please install Redis for Windows.")
                    sys.exit(1)
    
    def _start_web_server(self, port):
        """Start the web server"""
        venv_python = self.bench_path / "env" / "Scripts" / "python.exe"
        
        # Set environment variables
        env = os.environ.copy()
        env["PYTHONPATH"] = str(self.bench_path)
        
        # Start the server
        cmd = [str(venv_python), "-m", "frappe", "serve", "--port", str(port)]
        
        print(f"Starting web server on port {port}...")
        print(f"ERPNext will be available at http://localhost:{port}")
        
        try:
            subprocess.run(cmd, cwd=str(self.bench_path), env=env)
        except KeyboardInterrupt:
            print("\nShutting down server...")


def main():
    parser = argparse.ArgumentParser(description="Windows-native Frappe Bench CLI")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Init command
    init_parser = subparsers.add_parser("init", help="Initialize a new bench")
    init_parser.add_argument("bench_name", help="Name of the bench directory")
    init_parser.add_argument("--frappe-branch", default="version-15", help="Frappe branch to use")
    init_parser.add_argument("--python", help="Python executable path")
    
    # Get-app command
    get_app_parser = subparsers.add_parser("get-app", help="Get an app from repository")
    get_app_parser.add_argument("app_name", help="Name of the app")
    get_app_parser.add_argument("--branch", help="Git branch to clone")
    get_app_parser.add_argument("--git-url", help="Git repository URL")
    
    # New-site command
    new_site_parser = subparsers.add_parser("new-site", help="Create a new site")
    new_site_parser.add_argument("site_name", help="Name of the site")
    new_site_parser.add_argument("--admin-password", default="admin", help="Admin password")
    new_site_parser.add_argument("--mariadb-root-password", default="root", help="MariaDB root password")
    
    # Install-app command
    install_app_parser = subparsers.add_parser("install-app", help="Install app on site")
    install_app_parser.add_argument("app_name", help="Name of the app to install")
    install_app_parser.add_argument("--site", required=True, help="Site name")
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start development server")
    start_parser.add_argument("--port", type=int, default=8000, help="Port to run server on")
    
    args = parser.parse_args()
    
    if args.command == "init":
        bench_path = Path.cwd() / args.bench_name
        bench = WindowsBench(bench_path)
        bench.init_bench(args.frappe_branch, args.python)
    
    elif args.command == "get-app":
        bench = WindowsBench()
        bench.get_app(args.app_name, args.branch, args.git_url)
    
    elif args.command == "new-site":
        bench = WindowsBench()
        bench.new_site(args.site_name, args.admin_password, args.mariadb_root_password)
    
    elif args.command == "install-app":
        bench = WindowsBench()
        bench.install_app(args.site, args.app_name)
    
    elif args.command == "start":
        bench = WindowsBench()
        bench.start(args.port)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()

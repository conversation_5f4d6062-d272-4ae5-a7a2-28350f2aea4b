@echo off
REM ERPNext Native Windows Installation Batch File
REM This batch file installs ERPNext to run natively on Windows 11

echo ===============================================
echo    ERPNext Native Windows Installation
echo    No WSL Required - Pure Windows Setup
echo ===============================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges... Good!
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please right-click on this batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo Current directory: %CD%
echo.

REM Check if PowerShell script exists
if not exist "install_erpnext_windows_native.ps1" (
    echo ERROR: install_erpnext_windows_native.ps1 not found in current directory!
    echo Please ensure all installation files are in the same folder.
    echo.
    pause
    exit /b 1
)

REM Check if Windows bench files exist
if not exist "windows_bench.py" (
    echo ERROR: windows_bench.py not found in current directory!
    echo Please ensure all installation files are in the same folder.
    echo.
    pause
    exit /b 1
)

if not exist "windows_service_manager.py" (
    echo ERROR: windows_service_manager.py not found in current directory!
    echo Please ensure all installation files are in the same folder.
    echo.
    pause
    exit /b 1
)

echo Installation Options:
echo.
echo 1. Complete Installation (Recommended)
echo    - Installs all dependencies
echo    - Sets up ERPNext
echo    - Creates desktop shortcuts
echo.
echo 2. Complete Installation + Windows Services
echo    - Everything in option 1
echo    - Installs ERPNext as Windows service
echo    - Auto-starts on system boot
echo.
echo 3. Dependencies Only
echo    - Installs Python, Node.js, MariaDB, Redis, etc.
echo    - Skips ERPNext installation
echo.
echo 4. ERPNext Only
echo    - Assumes dependencies are already installed
echo    - Only installs ERPNext
echo.
echo 5. Custom Installation Path
echo    - Choose where to install ERPNext
echo.
echo 6. Exit
echo.

set /p "choice=Please select an option (1-6): "

if "%choice%"=="1" goto COMPLETE
if "%choice%"=="2" goto COMPLETE_WITH_SERVICE
if "%choice%"=="3" goto DEPS_ONLY
if "%choice%"=="4" goto ERPNEXT_ONLY
if "%choice%"=="5" goto CUSTOM_PATH
if "%choice%"=="6" goto EXIT
echo Invalid choice. Please try again.
echo.
goto :EOF

:COMPLETE
echo.
echo Starting complete ERPNext installation...
echo This will install ERPNext to C:\ERPNext
echo.
set /p "confirm=Do you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" goto :EOF

powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_windows_native.ps1"
goto COMPLETION

:COMPLETE_WITH_SERVICE
echo.
echo Starting complete ERPNext installation with Windows services...
echo This will install ERPNext to C:\ERPNext and set up Windows services
echo.
set /p "confirm=Do you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" goto :EOF

powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_windows_native.ps1" -InstallAsService
goto COMPLETION

:DEPS_ONLY
echo.
echo Installing dependencies only...
echo This will install Python, Node.js, MariaDB, Redis, and other dependencies
echo.
set /p "confirm=Do you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" goto :EOF

powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_windows_native.ps1" -SkipERPNext
goto COMPLETION

:ERPNEXT_ONLY
echo.
echo Installing ERPNext only...
echo This assumes all dependencies are already installed
echo.
set /p "confirm=Do you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" goto :EOF

powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_windows_native.ps1" -SkipDependencies
goto COMPLETION

:CUSTOM_PATH
echo.
set /p "install_path=Enter installation path (e.g., D:\ERPNext): "
if "%install_path%"=="" (
    echo Invalid path. Using default: C:\ERPNext
    set "install_path=C:\ERPNext"
)

echo.
echo Installing ERPNext to: %install_path%
echo.
set /p "confirm=Do you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" goto :EOF

powershell.exe -ExecutionPolicy Bypass -File "install_erpnext_windows_native.ps1" -InstallPath "%install_path%"
goto COMPLETION

:COMPLETION
if %errorLevel% == 0 (
    echo.
    echo ===============================================
    echo    ERPNext Installation Completed Successfully!
    echo ===============================================
    echo.
    echo ERPNext is now installed and ready to use.
    echo.
    echo Access Information:
    echo - URL: http://localhost:8000
    echo - Username: Administrator
    echo - Password: admin123
    echo.
    echo Desktop shortcuts have been created:
    echo - Start_ERPNext_Native.bat
    echo - Stop_ERPNext_Native.bat
    echo - ERPNext_Status.bat
    echo - Open_ERPNext.bat
    echo.
    echo To start ERPNext:
    echo 1. Double-click Start_ERPNext_Native.bat on your desktop, OR
    echo 2. Open Command Prompt and run the start script
    echo.
    echo Installation log saved to: C:\ERPNext\installation.log
    echo.
) else (
    echo.
    echo ===============================================
    echo    Installation Failed!
    echo ===============================================
    echo.
    echo The installation encountered an error.
    echo Please check the error messages above and try again.
    echo.
    echo Common solutions:
    echo 1. Ensure you're running as Administrator
    echo 2. Check your internet connection
    echo 3. Temporarily disable antivirus software
    echo 4. Ensure you have at least 20GB free disk space
    echo.
    echo For support, visit: https://discuss.erpnext.com/
    echo.
)

echo.
echo Additional Information:
echo.
echo System Requirements Met:
echo - Windows 11 (or Windows 10 version 2004+)
echo - Administrator privileges
echo - Internet connection
echo - At least 8GB RAM recommended
echo - At least 20GB free disk space
echo.
echo What was installed:
echo - Python 3.10+ with virtual environment
echo - Node.js 18+ with Yarn package manager
echo - MariaDB database server
echo - Redis cache server
echo - wkhtmltopdf for PDF generation
echo - Visual C++ Build Tools
echo - Frappe Framework version 15
echo - ERPNext version 15
echo - Windows-native bench CLI replacement
echo - Windows service management tools
echo.
echo Configuration files:
echo - ERPNext configuration: C:\ERPNext\erpnext_config.json
echo - Site configuration: C:\ERPNext\frappe-bench\sites\site1.local\site_config.json
echo - Common site config: C:\ERPNext\frappe-bench\sites\common_site_config.json
echo.
echo Log files location:
echo - Process logs: C:\ERPNext\frappe-bench\logs\
echo - Installation log: C:\ERPNext\installation.log
echo.
echo Troubleshooting:
echo - If ERPNext doesn't start, check if MariaDB and Redis services are running
echo - Use ERPNext_Status.bat to check service status
echo - Check log files for detailed error messages
echo - Ensure no other applications are using port 8000
echo.
echo For advanced users:
echo - Bench path: C:\ERPNext\frappe-bench
echo - Virtual environment: C:\ERPNext\frappe-bench\env
echo - Apps directory: C:\ERPNext\frappe-bench\apps
echo - Sites directory: C:\ERPNext\frappe-bench\sites
echo.

:EXIT
echo.
echo Thank you for installing ERPNext!
echo.
echo If you need help:
echo - Documentation: https://docs.erpnext.com/
echo - Community Forum: https://discuss.erpnext.com/
echo - GitHub: https://github.com/frappe/erpnext
echo.
echo Press any key to exit...
pause >nul
